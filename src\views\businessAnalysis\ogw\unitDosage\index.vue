<template>
  <div class="unitDosage-container">
    <div class="unitDosage-device">
      <span class="device-text">装置</span>
      <el-select
        clearable
        class="device-select"
        v-model="searchHzNo"
        placeholder="请选择"
        @change="getMedicine"
      >
        <el-option
          v-for="item in deviceOption"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div class="medice-radio">
      <el-radio
        @change="getEchartData"
        v-for="item in medicine"
        :key="item.material_code"
        v-model="radio"
        :label="item.material_code"
        >{{ item.chemical_name }}</el-radio
      >
    </div>
    <div class="unitDosage-chart">
      <LineChart
        v-if="showChart"
        :legendData="legendData"
        :xData="xData"
        :yData="yData"
      ></LineChart>
    </div>
    <div class="unitDosage-sum">
      <p>
        {{ deviceName }}，甲醇单位处理气量{{ year }}年
        <span class="text-light">最高</span>，
        <span class="text-light">高于</span>平均值
        <span class="text-light"
          >{{ ((maxValue / 10000) - average).toFixed(2) }}方/L</span
        >
      </p>
    </div>
  </div>
</template>
<script>
import LineChart from "@/components/charts/line/linesChart.vue";
import { getFocusMedicine, getFocusMedicineChart } from "@/api/ogw/index.js";
import { getProd } from "@/api/common.js";
export default {
  name: "unitDosage",
  components: {
    LineChart,
  },
  mounted() {
    this.getDevice();
    this.getMedicine();
  },
  data() {
    return {
      deviceOption: [],
      showChart: true,
      radio: "",
      medicine: [],
      year: "",
      maxValue: "",
      deviceName: "",
      searchHzNo: "",
      chartData: [],
    };
  },
  computed: {
    legendData() {
      return this.chartData.map((item) => item.deviceName);
    },
    xData() {
      const arr = [];
      this.chartData.forEach((item) => {
        item.data.forEach((i) => {
          arr.push(i.year);
        });
      });
      return Array.from(new Set(arr)).sort();
    },
    yData() {
      const arr = [];
      const maxArr = [];
      this.chartData.forEach((item) => {
        const obj = new Array(this.xData.length).fill(0);
        item.data.forEach((i) => {
          const y = this.xData.indexOf(i.year);
          if (y !== -1) {
            // 转换为万元单位（除以10000）并保留2位小数
            obj[y] = parseFloat((i.m3l / 10000).toFixed(2));
          }
        });
        maxArr.push(Math.max(...obj));
        arr.push(obj);
      });
      //  获取唯一最大值，并找出在原始数据中的位置
      const max = Math.max(...maxArr);
      const index = maxArr.indexOf(max);

      this.deviceName = this.chartData[index]?.deviceName;
      this.chartData[index]?.data.forEach((item) => {
        // 这里需要比较转换后的值
        if (parseFloat((item.m3l / 10000).toFixed(2)) === max) {
          this.year = item.year;
          // 保存原始值用于显示
          this.maxValue = item.m3l;
        }
      });
      return arr;
    },
    average() {
      const average =
        this.yData
          .reduce((acc, cur) => acc.concat(cur), [])
          .reduce((acc, cur) => acc + cur, 0) /
        this.yData.reduce((acc, cur) => acc.concat(cur), []).length;
      return average.toFixed(2);
    },
  },
  methods: {
    async getEchartData() {
      this.showChart = false;
      if (!this.radio) {
        this.$message.warning("该装置下没有化学药剂");
        return;
      };
      const res = await getFocusMedicineChart({
        materialCode: this.radio,
        hzNo: this.searchHzNo || "",
      });
      if (res.code === 200) {
        this.chartData = res.data;
        this.showChart = true;
      }
    },
    async getMedicine() {
      const res = await getFocusMedicine(this.searchHzNo);
      if (res.code === 200) {
        this.medicine = res.data;
        this.radio = this.medicine[0]?.material_code;
        this.getEchartData();
      }
    },
    async getDevice() {
      const res = await getProd();
      if (res.code === 200) {
        this.deviceOption = res.data[0].children?.map((item) => {
          return {
            value: item.hzNo,
            label: item.name,
          };
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.unitDosage-container {
  margin: 16px;

  .unitDosage-device {
    display: flex;
    align-items: center;

    .device-text {
      margin-right: 11px;

      font-family: Source Han Sans;
      font-size: 14px;
    }

    .device-select {
      width: 92%;
      height: 32px;
      border-radius: 6px;
      background: #263e78;
    }
  }

  .medice-radio {
    margin-top: 16px;

    .el-radio {
      margin-right: 16px;
    }
  }

  .unitDosage-chart {
    margin-top: 16px;
    width: 100%;
    height: 220px;
  }

  .unitDosage-sum {
    position: relative;
    width: 100%;
    margin-top: 16px;
    font-family: Source Han Sans;
    box-sizing: border-box;
    font-size: 14px;
    padding-left: 12px;
    padding-top: 8px;
    height: 56px;

    &::before,
    &::after,
    & span::before,
    & span::after {
      content: "";
      position: absolute;
      width: 12px;
      height: 12px;
      border: 2px solid #209fff;
      z-index: 1;
    }

    &::before {
      top: -1px;
      left: -1px;
      border-right: none;
      border-bottom: none;
    }

    &::after {
      bottom: -1px;
      right: -1px;
      border-left: none;
      border-top: none;
    }

    & span::before {
      /* 右上角 */
      top: -1px;
      right: -1px;
      border-left: none;
      border-bottom: none;
    }

    & span::after {
      /* 左下角 */
      bottom: -1px;
      left: -1px;
      border-right: none;
      border-top: none;
    }
  }
}

[data-theme="dark"] .device-text {
  color: #acc2e2;
}

[data-theme="dark"] .unitDosage-sum {
  background: #1a2e5e;
  border: 1px solid rgba(23, 131, 255, 0.5);
  color: #fff;
}

[data-theme="dark"] .text-light {
  color: #2de8e8;
}

[data-theme="tint"] .device-text {
  color: rgba(0, 0, 0, 0.85);
}

[data-theme="tint"] .unitDosage-sum {
  color: rgba(0, 0, 0, 0.85);
}

[data-theme="tint"] .text-light {
  color: #1677ff;
}

::v-deep .el-radio__inner {
  border: 1px solid #1783ff;
  background: rgba(23, 131, 255, 0.3);
}

::v-deep .el-radio__label {
  color: #acc2e2;
}
</style>
