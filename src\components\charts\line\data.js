var img = [
  "@/assets/tableicon/line1.png",
  "@/assets/tableicon/line2.png",
  "@/assets/tableicon/line3.png",
];
var color = ["#00f8ff", "#00f15a", "#0696f9", "#dcf776"];

export function getOption(xData, yData, Line) {
  // console.log("获取的数据", yData);

  var datas = [];
  Line.map((item, index) => {
    datas.push({
      name: item,
      type: "line",
      yAxisIndex: 1,
      data: yData[index],
      itemStyle: {
        normal: {
          borderWidth: 5,
          color: color[index],
        },
      },
      markLine: {
        symbol: "none",
        lineStyle: {
          color: color[index],
          type:"solid",
        },
        data:[
          {
            name:"平均值",
            yAxis: yData[index].reduce((a, b) => a + b, 0) / yData[index].length, // 计算平均值
          }
        ]
      }
    });
  });

  let option = {
    backgroundColor: "#0e2147",
    grid: {
      left: "36px",
      bottom: "32px",
    },
    legend: {
      type: "scroll",
      data: Line,
      itemWidth: 18,
      itemHeight: 12,
      textStyle: {
        color: "#00ffff",
        fontSize: 14,
      },
    },
    yAxis: [
      {
        type: "value",
        name: "万元",
        position: "left",
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      {
        type: "value",
        position: "left",
        name: "万元",
        nameLocation: "end",
        nameGap: 15,
        nameTextStyle: {
          color: "#00FFFF",
          fontSize: 12,
          fontWeight: "normal",
          padding: [0, 0, 5, 0], // 上右下左的内边距
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "rgba(135,140,147,0.8)",
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          formatter: function(value) {
            // 确保数值显示整齐，保留合适的小数位数
            if (value === 0) return "0";
            if (value < 1) return value.toFixed(2);
            if (value < 10) return value.toFixed(1);
            return Math.round(value).toString();
          },
          color: "#fff",
          fontSize: 14,
          margin: 8, // 增加标签与轴线的距离
          align: "right", // 右对齐，使数值显示更整齐
        },
      },
    ],
    xAxis: [
      {
        type: "category",
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#6A989E",
          },
        },
        axisLabel: {
          inside: false,
          textStyle: {
            color: "#fff", // x轴颜色
            fontWeight: "normal",
            fontSize: "14",
            lineHeight: 22,
          },
        },
        data: xData,
      },
    ],
    series: datas,
  };
  return option;
}
